/**
 * AI Chat Store
 *
 * Global state management for AI chat functionality
 * Provides centralized access to chat state, conversation history, and actions
 */

import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { useAuthStore } from './auth';
import { getAiChatService, type ChatMessage, type AIAction, type AIChatResponse, type StreamingChatResponse } from '../services/aiChatService';

// Enhanced chat message interface
interface EnhancedChatMessage extends ChatMessage {
  id: string;
  timestamp: Date;
  actions?: AIAction[];
  suggestions?: string[];
}

export const useAIChatStore = defineStore('aiChat', () => {
  // State
  const isOpen = ref(false);
  const isLoading = ref(false);
  const hasUnreadMessage = ref(false);
  const messages = ref<EnhancedChatMessage[]>([]);
  const conversationId = ref<string | null>(null);
  const isLoadingHistory = ref(false);

  // Computed
  const authStore = useAuthStore();

  // Current route context (to be set by components)
  const currentRoute = ref<string>('unknown');

  const messageHistory = computed(() =>
    messages.value.map(msg => ({
      role: msg.role,
      content: msg.content
    }))
  );

  const lastMessage = computed(() =>
    messages.value.length > 0 ? messages.value[messages.value.length - 1] : null
  );

  // Actions
  const setCurrentRoute = (routeName: string) => {
    currentRoute.value = routeName;
  };

  const toggleChat = () => {
    isOpen.value = !isOpen.value;
    if (isOpen.value) {
      hasUnreadMessage.value = false;
    }
  };

  const openChat = () => {
    isOpen.value = true;
    hasUnreadMessage.value = false;
  };

  const closeChat = () => {
    isOpen.value = false;
  };

  const addMessage = (message: Omit<EnhancedChatMessage, 'id' | 'timestamp'>): EnhancedChatMessage => {
    const newMessage: EnhancedChatMessage = {
      ...message,
      id: crypto.randomUUID(),
      timestamp: new Date()
    };

    messages.value.push(newMessage);

    // Mark as unread if chat is closed and it's an assistant message
    if (!isOpen.value && message.role === 'assistant') {
      hasUnreadMessage.value = true;
    }

    return newMessage;
  };

  const updateMessage = (messageId: string, updates: Partial<EnhancedChatMessage>) => {
    const messageIndex = messages.value.findIndex(msg => msg.id === messageId);
    if (messageIndex !== -1) {
      messages.value[messageIndex] = {
        ...messages.value[messageIndex],
        ...updates
      };
    }
  };

  const clearMessages = () => {
    messages.value = [];
    conversationId.value = null;
  };

  const setLoading = (loading: boolean) => {
    isLoading.value = loading;
  };

  const setConversationId = (id: string) => {
    conversationId.value = id;
  };

  // Load conversation history
  const loadConversationHistory = async (convId: string) => {
    try {
      const history = await getAiChatService().getConversationHistory(convId);
      // Convert to enhanced messages
      messages.value = history.map(msg => ({
        ...msg,
        id: crypto.randomUUID(),
        timestamp: new Date()
      }));
    } catch (error) {
      console.error('Failed to load conversation history:', error);
    }
  };

  // Start new conversation
  const startNewConversation = async () => {
    messages.value = [];
    conversationId.value = null;
    initializeChat();
  };

  // Send AI message with streaming support
  const sendAIMessage = async (
    messageText: string,
    useStreaming: boolean = false
  ): Promise<EnhancedChatMessage> => {
    if (!messageText.trim()) {
      throw new Error('Message cannot be empty');
    }

    // Add user message
    const userMessage = addMessage({
      role: 'user',
      content: messageText.trim()
    });

    setLoading(true);

    try {
      if (useStreaming) {
        // Create initial AI message for streaming
        const aiMessage = addMessage({
          role: 'assistant',
          content: ''
        });

        // Use streaming service
        await getAiChatService().sendStreamingMessage(
          messageText,
          conversationId.value || undefined,
          currentRoute.value,
          (chunk: StreamingChatResponse) => {
            if (chunk.type === 'content' && chunk.content) {
              const currentMessage = messages.value.find(msg => msg.id === aiMessage.id);
              updateMessage(aiMessage.id, {
                content: (currentMessage?.content || '') + chunk.content
              });
            } else if (chunk.type === 'complete') {
              updateMessage(aiMessage.id, {
                actions: chunk.actions || [],
                suggestions: chunk.suggestions || []
              });
              if (chunk.conversation_id) {
                conversationId.value = chunk.conversation_id;
              }
            }
          }
        );

        return aiMessage;
      } else {
        // Use non-streaming service
        const response = await getAiChatService().sendMessage(
          messageText,
          conversationId.value || undefined,
          currentRoute.value
        );

        // Set conversation ID if returned
        if (response.conversation_id) {
          conversationId.value = response.conversation_id;
        }

        // Create AI message with response
        // Handle both response formats: {response: "..."} and {message: "..."}
        const responseContent = response.response || response.message || 'No response received';
        const aiMessage = addMessage({
          role: 'assistant',
          content: responseContent,
          actions: response.actions || [],
          suggestions: response.suggestions || []
        });

        return aiMessage;
      }
    } catch (error) {
      console.error('Error sending AI message:', error);

      // Add error message
      const errorMessage = addMessage({
        role: 'assistant',
        content: 'I apologize, but I encountered an issue processing your request. Please try again in a moment.',
        actions: [
          { type: 'action', label: 'Try Again', icon: 'refresh', action: 'retry', color: 'primary' }
        ]
      });

      throw error;
    } finally {
      setLoading(false);

      // Trigger scroll to bottom
      setTimeout(() => {
        window.dispatchEvent(new CustomEvent('ai-trigger-scroll'));
      }, 100);
    }
  };



  // Initialize with welcome message if no messages exist
  const initializeChat = () => {
    if (messages.value.length === 0) {
      const authStore = useAuthStore();
      const isAuthenticated = authStore.isAuthenticated;

      const welcomeMessage = isAuthenticated
        ? `Hello! I'm your ZbInnovation AI Assistant. I can help you navigate the platform, find opportunities, and connect with the right people. What would you like to know?`
        : `Welcome to ZbInnovation! I'm your AI Assistant. I can help you learn about our innovation ecosystem and guide you through getting started. How can I assist you today?`;

      addMessage({
        role: 'assistant',
        content: welcomeMessage,
        suggestions: isAuthenticated
          ? [
              "How can I improve my profile?",
              "Show me networking opportunities",
              "Help me find relevant events",
              "What funding options are available?"
            ]
          : [
              "How do I sign up for the platform?",
              "What features are available?",
              "Tell me about the innovation community",
              "How can I connect with investors?"
            ]
      });
    }
  };

  return {
    // State
    isOpen,
    isLoading,
    hasUnreadMessage,
    messages,
    conversationId,
    isLoadingHistory,

    // Computed
    messageHistory,
    lastMessage,

    // Actions
    setCurrentRoute,
    toggleChat,
    openChat,
    closeChat,
    addMessage,
    updateMessage,
    clearMessages,
    setLoading,
    setConversationId,
    initializeChat,
    sendAIMessage,
    loadConversationHistory,
    startNewConversation
  };
});
