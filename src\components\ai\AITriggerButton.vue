<template>
  <q-btn
    :color="color"
    :icon="icon"
    :label="label"
    :size="size"
    :outline="outline"
    :flat="flat"
    :round="round"
    :loading="loading"
    :disable="disabled"
    @click="handleTrigger"
    class="ai-trigger-button"
    :class="buttonClass"
    :data-testid="`ai-trigger-${triggerKey}`"
  >
    <q-tooltip v-if="tooltip" class="bg-primary">
      {{ tooltip }}
    </q-tooltip>
  </q-btn>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useAIChatStore } from '../../stores/aiChatStore';
import { useAuthStore } from '../../stores/auth';

interface Props {
  triggerKey: string;
  label: string;
  icon?: string;
  color?: string;
  size?: string;
  outline?: boolean;
  flat?: boolean;
  round?: boolean;
  disabled?: boolean;
  tooltip?: string;
  context?: string;
  message?: string;
  variant?: 'default' | 'compact' | 'minimal';
}

const props = withDefaults(defineProps<Props>(), {
  icon: 'psychology',
  color: 'primary',
  size: 'md',
  outline: false,
  flat: false,
  round: false,
  disabled: false,
  context: 'general',
  variant: 'default'
});

const emit = defineEmits<{
  triggered: [triggerKey: string];
  success: [triggerKey: string];
  error: [triggerKey: string, error: Error];
}>();

const aiChatStore = useAIChatStore();
const authStore = useAuthStore();
const loading = ref(false);

const buttonClass = computed(() => {
  const classes = [`ai-trigger-button--${props.variant}`];
  
  if (props.context) {
    classes.push(`ai-trigger-button--${props.context}`);
  }
  
  return classes.join(' ');
});

// Predefined trigger messages based on trigger key
const getTriggerMessage = (key: string): string => {
  const messages: Record<string, string> = {
    // Profile triggers
    'complete_profile': 'Help me complete my profile effectively',
    'profile_optimization': 'How can I optimize my profile for better visibility?',
    'skills_showcase': 'How do I effectively showcase my skills and experience?',
    
    // Matchmaking triggers
    'find_matches': 'Help me find people and opportunities that match my profile',
    'connection_strategy': 'What\'s the best strategy for building connections in my field?',
    'networking': 'Give me networking advice and help me find relevant people to connect with',
    
    // Content triggers
    'content_discovery': 'Help me discover relevant content and discussions',
    'content_recommendations': 'What type of content should I create to engage my audience?',
    'post_assistance': 'Help me create an engaging post',
    
    // Collaboration triggers
    'collaboration': 'Help me find collaboration opportunities',
    'find_mentors': 'How do I find and connect with mentors in my field?',
    'find_investors': 'Help me find potential investors for my innovation',
    
    // Quick actions
    'platform_guidance': 'Help me navigate and make the most of this platform',
    'next_steps': 'What should I do next to grow my presence on the platform?',
    'opportunities': 'Show me relevant opportunities based on my profile',
    
    // Context-specific triggers
    'dashboard_help': 'Help me understand my dashboard and next steps',
    'community_help': 'Help me engage with the community effectively',
    'profile_help': 'Guide me through optimizing my profile',
    'feed_help': 'Help me discover and engage with relevant content'
  };
  
  return messages[key] || `Help me with ${key.replace(/_/g, ' ')}`;
};

const handleTrigger = async () => {
  if (loading.value || props.disabled) return;
  
  loading.value = true;
  emit('triggered', props.triggerKey);
  
  try {
    // Set current route context for AI
    aiChatStore.setCurrentRoute(props.context);
    
    // Open AI chat if not already open
    if (!aiChatStore.isOpen) {
      aiChatStore.openChat();
      
      // Wait a bit for the chat to open and scroll
      await new Promise(resolve => setTimeout(resolve, 300));
    }
    
    // Get the trigger message
    const message = props.message || getTriggerMessage(props.triggerKey);
    
    // Send the AI message
    await aiChatStore.sendAIMessage(message);
    
    // Trigger scroll to bottom
    setTimeout(() => {
      window.dispatchEvent(new CustomEvent('ai-trigger-scroll'));
    }, 100);
    
    emit('success', props.triggerKey);
    
  } catch (error: any) {
    console.error('AI trigger error:', error);
    emit('error', props.triggerKey, error);
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.ai-trigger-button {
  transition: all 0.3s ease;
  font-weight: 500;
}

.ai-trigger-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.ai-trigger-button--compact {
  font-size: 0.875rem;
}

.ai-trigger-button--minimal {
  font-size: 0.75rem;
  padding: 4px 8px;
}

.ai-trigger-button--dashboard {
  border-radius: 8px;
}

.ai-trigger-button--community {
  border-radius: 6px;
}

.ai-trigger-button--profile {
  border-radius: 12px;
}

/* Pulse animation for important triggers */
.ai-trigger-button.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(25, 118, 210, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(25, 118, 210, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(25, 118, 210, 0);
  }
}

/* Context-specific styling */
.ai-trigger-button--dashboard {
  background: linear-gradient(45deg, var(--q-primary) 0%, var(--q-secondary) 100%);
}

.ai-trigger-button--community {
  background: linear-gradient(45deg, var(--q-accent) 0%, var(--q-primary) 100%);
}

.ai-trigger-button--profile {
  background: linear-gradient(45deg, var(--q-positive) 0%, var(--q-accent) 100%);
}
</style>
