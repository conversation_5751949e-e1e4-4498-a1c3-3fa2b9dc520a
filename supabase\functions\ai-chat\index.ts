import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'jsr:@supabase/supabase-js@2';

// Types for AI chat
interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp?: string;
}

interface ChatRequest {
  message: string;
  conversation_history?: ChatMessage[];
  user_context?: {
    user_id?: string;
    profile_status?: 'not_started' | 'in_progress' | 'completed';
    profile_type?: string;
  };
  rag_enabled?: boolean;
  max_context_items?: number;
}

interface ChatResponse {
  success: boolean;
  message?: string;
  conversation_id?: string;
  rag_context_used?: any[];
  processing_time_ms: number;
  error?: string;
}

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// DeepSeek API configuration
const DEEPSEEK_API_KEY = Deno.env.get('DEEPSEEK_API_KEY') || '***********************************';
const DEEPSEEK_BASE_URL = 'https://api.deepseek.com';

/**
 * Generate embedding for user query
 */
async function generateQueryEmbedding(text: string): Promise<number[]> {
  try {
    const { data, error } = await supabase.functions.invoke('embed', {
      body: {
        input: text,
        model: 'gte-small'
      }
    });

    if (error) {
      console.error('Query embedding error:', error);
      throw new Error(`Failed to generate query embedding: ${error.message}`);
    }

    return data.embedding;
  } catch (error) {
    console.error('Error generating query embedding:', error);
    throw error;
  }
}

/**
 * Get RAG context for user query
 */
async function getRAGContext(
  userMessage: string,
  userId?: string,
  maxItems: number = 10
): Promise<any[]> {
  try {
    // Generate embedding for user query
    const queryEmbedding = await generateQueryEmbedding(userMessage);
    
    // Get relevant context using our RAG function
    const { data: context, error } = await supabase.rpc('get_rag_context', {
      query_embedding: `[${queryEmbedding.join(',')}]`,
      user_id_param: userId || null,
      context_types: null, // Get all types
      max_context_items: maxItems,
      similarity_threshold: 0.6
    });

    if (error) {
      console.error('RAG context error:', error);
      return [];
    }

    return context || [];
  } catch (error) {
    console.error('Error getting RAG context:', error);
    return [];
  }
}

/**
 * Get user profile status for context
 */
async function getUserProfileStatus(userId: string): Promise<{
  profile_status: string;
  profile_type?: string;
  profile_name?: string;
}> {
  try {
    // Check different profile types
    const profileChecks = [
      { table: 'innovator_profiles', type: 'innovator' },
      { table: 'investor_profiles', type: 'investor' },
      { table: 'mentor_profiles', type: 'mentor' },
      { table: 'professional_profiles', type: 'professional' }
    ];

    for (const check of profileChecks) {
      const { data, error } = await supabase
        .from(check.table)
        .select('profile_name, completion_percentage')
        .eq('user_id', userId)
        .single();

      if (!error && data) {
        const completionPercentage = data.completion_percentage || 0;
        return {
          profile_status: completionPercentage >= 80 ? 'completed' : 
                         completionPercentage >= 20 ? 'in_progress' : 'not_started',
          profile_type: check.type,
          profile_name: data.profile_name
        };
      }
    }

    return { profile_status: 'not_started' };
  } catch (error) {
    console.error('Error getting user profile status:', error);
    return { profile_status: 'unknown' };
  }
}

/**
 * Build system prompt with context
 */
function buildSystemPrompt(
  userContext: any,
  ragContext: any[],
  platformStats: any
): string {
  const authStatus = userContext.user_id ? 'authenticated' : 'guest';
  const profileStatus = userContext.profile_status || 'unknown';
  const profileType = userContext.profile_type || 'unknown';

  let systemPrompt = `You are a helpful AI assistant for the ZbInnovation platform - Zimbabwe's premier innovation ecosystem.

User Context:
- Authentication: ${authStatus}
- Profile Status: ${profileStatus}
- Profile Type: ${profileType}`;

  if (userContext.profile_name) {
    systemPrompt += `\n- Profile Name: ${userContext.profile_name}`;
  }

  // Add platform context
  if (platformStats) {
    systemPrompt += `\n\nPlatform Overview:
- Total Profiles: ${platformStats.total_profiles || 'N/A'}
- Total Posts: ${platformStats.total_posts || 'N/A'}
- Active Community: Innovators, Investors, Mentors, Professionals`;
  }

  // Add RAG context if available
  if (ragContext && ragContext.length > 0) {
    systemPrompt += `\n\nRelevant Platform Content:`;
    ragContext.forEach((item, index) => {
      systemPrompt += `\n${index + 1}. [${item.content_type}] ${item.content_snippet}`;
    });
  }

  systemPrompt += `\n\nInstructions:
- Provide helpful, accurate responses about the innovation ecosystem
- Reference specific platform content when relevant
- Suggest appropriate actions based on user's profile status
- Be encouraging and supportive of innovation and entrepreneurship
- If you don't know something, say so rather than guessing
- Keep responses concise but informative`;

  return systemPrompt;
}

/**
 * Call DeepSeek API for chat completion
 */
async function callDeepSeekAPI(
  messages: ChatMessage[],
  stream: boolean = false
): Promise<string> {
  try {
    const response = await fetch(`${DEEPSEEK_BASE_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${DEEPSEEK_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'deepseek-chat',
        messages: messages.map(msg => ({
          role: msg.role,
          content: msg.content
        })),
        stream: stream,
        temperature: 0.7,
        max_tokens: 1000
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`DeepSeek API error: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    
    if (!data.choices || !data.choices[0] || !data.choices[0].message) {
      throw new Error('Invalid response format from DeepSeek API');
    }

    return data.choices[0].message.content;
  } catch (error) {
    console.error('DeepSeek API error:', error);
    throw error;
  }
}

/**
 * Main Edge Function handler
 */
Deno.serve(async (req: Request) => {
  const startTime = Date.now();
  
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
      },
    });
  }

  if (req.method !== 'POST') {
    return new Response(
      JSON.stringify({ error: 'Method not allowed' }),
      { 
        status: 405,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        }
      }
    );
  }

  try {
    const body = await req.json() as ChatRequest;
    const { 
      message, 
      conversation_history = [], 
      user_context = {},
      rag_enabled = true,
      max_context_items = 8
    } = body;

    console.log(`Processing AI chat request: "${message.substring(0, 50)}..."`);

    // Get user profile status if user_id provided
    let enhancedUserContext = { ...user_context };
    if (user_context.user_id) {
      const profileStatus = await getUserProfileStatus(user_context.user_id);
      enhancedUserContext = { ...enhancedUserContext, ...profileStatus };
    }

    // Get RAG context if enabled
    let ragContext: any[] = [];
    if (rag_enabled) {
      ragContext = await getRAGContext(
        message, 
        user_context.user_id, 
        max_context_items
      );
      console.log(`Retrieved ${ragContext.length} RAG context items`);
    }

    // Get platform stats for context
    const { data: platformStats } = await supabase.rpc('get_platform_stats_for_ai');

    // Build system prompt
    const systemPrompt = buildSystemPrompt(enhancedUserContext, ragContext, platformStats);

    // Prepare messages for DeepSeek
    const messages: ChatMessage[] = [
      { role: 'system', content: systemPrompt },
      ...conversation_history.slice(-6), // Keep last 6 messages for context
      { role: 'user', content: message }
    ];

    // Call DeepSeek API
    let aiResponse: string;
    try {
      aiResponse = await callDeepSeekAPI(messages);
    } catch (error) {
      console.error('DeepSeek API failed, using fallback:', error);
      aiResponse = `I'm currently experiencing technical difficulties connecting to our AI service. However, I can see you're asking about our innovation platform. 

Based on our platform data, we have ${platformStats?.total_profiles || 'many'} profiles and ${platformStats?.total_posts || 'numerous'} posts from our community. 

Please try your question again in a moment, or feel free to explore the platform directly. If you need immediate assistance, you can browse our community profiles and posts manually.`;
    }

    const processingTime = Date.now() - startTime;

    const response: ChatResponse = {
      success: true,
      message: aiResponse,
      rag_context_used: ragContext.map(item => ({
        type: item.content_type,
        relevance: item.relevance_score,
        snippet: item.content_snippet.substring(0, 100) + '...'
      })),
      processing_time_ms: processingTime
    };

    console.log(`AI chat completed in ${processingTime}ms`);

    return new Response(
      JSON.stringify(response),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      }
    );

  } catch (error) {
    console.error('AI chat error:', error);
    
    const processingTime = Date.now() - startTime;
    
    return new Response(
      JSON.stringify({
        success: false,
        processing_time_ms: processingTime,
        error: error.message
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      }
    );
  }
});
