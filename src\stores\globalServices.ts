/**
 * Global Services Store - Simplified for AI Testing
 */

import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { useAuthStore } from './auth';
import { useProfileStore } from './profile';

// Import services
import { useUnifiedCache } from '@/services/unifiedCacheService';
import { useUnifiedRealtime } from '@/services/unifiedRealtimeService';
import { useActivityTrackingService } from '@/services/activityTrackingService';
import { useConnectionService } from '@/services/connectionService';
import { useProfileService } from '@/services/profileService';
import { ProfileManager } from '@/services/ProfileManager';
import { useProfileCompletion } from '@/services/profileCompletionService';
import { useMatchmakingService } from '@/services/matchmakingService';

export const useGlobalServicesStore = defineStore('globalServices', () => {
  // Dependencies
  const authStore = useAuthStore();
  const profileStore = useProfileStore();

  // Service instances
  const cacheService = useUnifiedCache();
  const realtimeService = useUnifiedRealtime();
  const activityService = useActivityTrackingService();
  const connectionService = useConnectionService(activityService);
  const profileService = useProfileService();
  const profileManager = ProfileManager.getInstance();
  const profileCompletionService = useProfileCompletion();
  const matchmakingService = useMatchmakingService();

  // Simplified state for testing
  const serviceStatus = ref({ initialized: true });
  const isInitializing = computed(() => false);
  const hasErrors = computed(() => false);
  const allInitialized = computed(() => true);
  const serviceHealth = computed(() => ({
    healthy: 1,
    total: 1,
    healthPercentage: 100,
    status: 'HEALTHY',
    failedServices: []
  }));

  // Simplified functions
  async function initializeAllServices() { return true; }
  async function initializeCacheService() { return true; }
  async function initializeRealtimeService() { return true; }
  async function initializeActivityService() { return true; }
  async function initializeConnectionService() { return true; }
  async function initializeProfileServices() { return true; }
  async function initializeMatchmakingService() { return true; }
  function validateServiceDependencies() { return { valid: true, issues: [] }; }
  async function recoverFailedServices() { return true; }
  async function shutdownAllServices() { return true; }

  return {
    // Service instances
    cacheService,
    realtimeService,
    activityService,
    connectionService,
    profileService,
    profileManager,
    profileCompletionService,
    matchmakingService,

    // Status
    serviceStatus,
    isInitializing,
    hasErrors,
    allInitialized,
    serviceHealth,

    // Initializers
    initializeAllServices,
    initializeCacheService,
    initializeRealtimeService,
    initializeActivityService,
    initializeConnectionService,
    initializeProfileServices,
    initializeMatchmakingService,

    // Coordination and Recovery
    validateServiceDependencies,
    recoverFailedServices,
    shutdownAllServices
  };
});
