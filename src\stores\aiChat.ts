/**
 * AI Chat Store - Clean Implementation
 *
 * Global state management for AI chat functionality
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useAuthStore } from './auth'
import { getAiChatService, type ChatMessage, type AIChatResponse } from '../services/aiChatService'

// Enhanced chat message interface
interface EnhancedChatMessage extends ChatMessage {
  id: string
  timestamp: Date
}

export const useAIChatStore = defineStore('aiChat', () => {
  // State
  const isOpen = ref(false)
  const isLoading = ref(false)
  const hasUnreadMessage = ref(false)
  const messages = ref<EnhancedChatMessage[]>([])
  const currentRoute = ref<string>('unknown')

  // Computed
  const authStore = useAuthStore()

  const messageHistory = computed(() =>
    messages.value.map(msg => ({
      role: msg.role,
      content: msg.content
    }))
  )

  const lastMessage = computed(() =>
    messages.value.length > 0 ? messages.value[messages.value.length - 1] : null
  )

  // Actions
  const setCurrentRoute = (routeName: string) => {
    currentRoute.value = routeName
  }

  const toggleChat = () => {
    isOpen.value = !isOpen.value
    if (isOpen.value) {
      hasUnreadMessage.value = false
      initializeChat()
    }
  }

  const openChat = () => {
    isOpen.value = true
    hasUnreadMessage.value = false
    initializeChat()
  }

  const closeChat = () => {
    isOpen.value = false
  }

  const addMessage = (message: Omit<EnhancedChatMessage, 'id' | 'timestamp'>): EnhancedChatMessage => {
    const newMessage: EnhancedChatMessage = {
      ...message,
      id: crypto.randomUUID(),
      timestamp: new Date()
    }

    messages.value.push(newMessage)

    // Mark as unread if chat is closed and it's an assistant message
    if (!isOpen.value && message.role === 'assistant') {
      hasUnreadMessage.value = true
    }

    return newMessage
  }

  const updateMessage = (messageId: string, updates: Partial<EnhancedChatMessage>) => {
    const messageIndex = messages.value.findIndex(msg => msg.id === messageId)
    if (messageIndex !== -1) {
      messages.value[messageIndex] = {
        ...messages.value[messageIndex],
        ...updates
      }
    }
  }

  const clearMessages = () => {
    messages.value = []
  }

  const setLoading = (loading: boolean) => {
    isLoading.value = loading
  }

  // Send AI message
  const sendAIMessage = async (messageText: string): Promise<EnhancedChatMessage> => {
    if (!messageText.trim()) {
      throw new Error('Message cannot be empty')
    }

    console.log('🚀 Sending AI message:', messageText.substring(0, 50) + '...')

    // Add user message
    const userMessage = addMessage({
      role: 'user',
      content: messageText.trim()
    })

    setLoading(true)

    try {
      // Get conversation history for context
      const conversationHistory = messageHistory.value.slice(-6) // Last 6 messages

      // Call AI service
      const response: AIChatResponse = await getAiChatService().sendMessage(
        messageText,
        conversationHistory,
        currentRoute.value
      )

      console.log('📨 AI service response:', {
        success: response.success,
        hasMessage: !!response.message,
        messageLength: response.message?.length || 0,
        error: response.error
      })

      if (!response.success || !response.message) {
        throw new Error(response.error || 'AI service returned no response')
      }

      // Add AI response message
      const aiMessage = addMessage({
        role: 'assistant',
        content: response.message
      })

      console.log('✅ AI message added to chat')
      return aiMessage

    } catch (error: any) {
      console.error('💥 Error sending AI message:', error)

      // Add error message
      const errorMessage = addMessage({
        role: 'assistant',
        content: `I apologize, but I'm experiencing technical difficulties right now. 

**Error:** ${error.message}

Please try again in a moment. If the problem persists, you can:
- Refresh the page and try again
- Contact our support team for assistance

I'm here to help once the issue is resolved!`
      })

      throw error
    } finally {
      setLoading(false)

      // Trigger scroll to bottom
      setTimeout(() => {
        window.dispatchEvent(new CustomEvent('ai-trigger-scroll'))
      }, 100)
    }
  }

  // Initialize with welcome message if no messages exist
  const initializeChat = () => {
    if (messages.value.length === 0) {
      const isAuthenticated = authStore.isAuthenticated

      const welcomeMessage = isAuthenticated
        ? `Hello! I'm your ZbInnovation AI Assistant. I can help you navigate the platform, find opportunities, and connect with the right people. What would you like to know?`
        : `Welcome to ZbInnovation! I'm your AI Assistant. I can help you learn about our innovation ecosystem and guide you through getting started. How can I assist you today?`

      addMessage({
        role: 'assistant',
        content: welcomeMessage
      })
    }
  }

  return {
    // State
    isOpen,
    isLoading,
    hasUnreadMessage,
    messages,

    // Computed
    messageHistory,
    lastMessage,

    // Actions
    setCurrentRoute,
    toggleChat,
    openChat,
    closeChat,
    addMessage,
    updateMessage,
    clearMessages,
    setLoading,
    initializeChat,
    sendAIMessage
  }
})
