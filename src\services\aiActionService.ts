/**
 * AI Action Service
 * 
 * Handles execution of AI action buttons including navigation, dialogs, and triggers
 */

import { useRouter } from 'vue-router'
import { Dialog } from 'quasar'
import type { AIActionButton } from './aiChatService'
import { useAuthStore } from '@/stores/auth'
import { triggerAuthDialog } from './authDialogService'

interface ActionExecutionResult {
  success: boolean
  message?: string
  error?: string
}

class AIActionService {
  /**
   * Execute an AI action button
   */
  async executeAction(action: AIActionButton): Promise<ActionExecutionResult> {
    try {
      console.log('🎯 Executing AI action:', action)

      // Check authentication requirement
      if (action.requires_auth) {
        const authStore = useAuthStore()
        if (!authStore.isAuthenticated) {
          console.log('🔒 Action requires authentication, triggering auth dialog')
          const authSuccess = await triggerAuthDialog('signin')
          if (!authSuccess) {
            return {
              success: false,
              message: 'Authentication required to perform this action'
            }
          }
        }
      }

      // Execute action based on type
      switch (action.action_type) {
        case 'navigation':
          return await this.executeNavigationAction(action)
        
        case 'dialog':
          return await this.executeDialogAction(action)
        
        case 'trigger':
          return await this.executeTriggerAction(action)
        
        case 'external':
          return await this.executeExternalAction(action)
        
        default:
          throw new Error(`Unknown action type: ${action.action_type}`)
      }

    } catch (error: any) {
      console.error('❌ Error executing AI action:', error)
      return {
        success: false,
        error: error.message || 'Failed to execute action'
      }
    }
  }

  /**
   * Execute navigation action
   */
  private async executeNavigationAction(action: AIActionButton): Promise<ActionExecutionResult> {
    try {
      const router = useRouter()
      const { route, params } = action.action_data

      if (!route) {
        throw new Error('Navigation action missing route')
      }

      console.log('🧭 Navigating to:', { route, params })

      // Handle different route formats
      if (route.startsWith('/')) {
        // Direct path navigation
        await router.push({ path: route, query: params })
      } else {
        // Named route navigation
        await router.push({ name: route, params, query: params })
      }

      return {
        success: true,
        message: `Navigated to ${route}`
      }

    } catch (error: any) {
      console.error('❌ Navigation error:', error)
      return {
        success: false,
        error: `Navigation failed: ${error.message}`
      }
    }
  }

  /**
   * Execute dialog action
   */
  private async executeDialogAction(action: AIActionButton): Promise<ActionExecutionResult> {
    try {
      const { dialog, params } = action.action_data

      if (!dialog) {
        throw new Error('Dialog action missing dialog type')
      }

      console.log('💬 Opening dialog:', { dialog, params })

      switch (dialog) {
        case 'auth-signin':
          await triggerAuthDialog('signin')
          break
        
        case 'auth-signup':
          await triggerAuthDialog('signup')
          break
        
        case 'profile-completion':
          await this.openProfileCompletionDialog(params)
          break
        
        case 'message-user':
          await this.openMessageDialog(params)
          break
        
        case 'post-creation':
          await this.openPostCreationDialog(params)
          break
        
        default:
          throw new Error(`Unknown dialog type: ${dialog}`)
      }

      return {
        success: true,
        message: `Opened ${dialog} dialog`
      }

    } catch (error: any) {
      console.error('❌ Dialog error:', error)
      return {
        success: false,
        error: `Dialog failed: ${error.message}`
      }
    }
  }

  /**
   * Execute trigger action
   */
  private async executeTriggerAction(action: AIActionButton): Promise<ActionExecutionResult> {
    try {
      const { trigger_key, params } = action.action_data

      if (!trigger_key) {
        throw new Error('Trigger action missing trigger_key')
      }

      console.log('🎯 Executing trigger:', { trigger_key, params })

      // Import trigger service dynamically to avoid circular dependencies
      const { useAiChatTriggerService } = await import('./aiChatTriggerService')
      const triggerService = useAiChatTriggerService()

      await triggerService.triggerChat(trigger_key, params?.context)

      return {
        success: true,
        message: `Triggered ${trigger_key}`
      }

    } catch (error: any) {
      console.error('❌ Trigger error:', error)
      return {
        success: false,
        error: `Trigger failed: ${error.message}`
      }
    }
  }

  /**
   * Execute external action
   */
  private async executeExternalAction(action: AIActionButton): Promise<ActionExecutionResult> {
    try {
      const { url } = action.action_data

      if (!url) {
        throw new Error('External action missing URL')
      }

      console.log('🌐 Opening external URL:', url)

      // Open in new tab/window
      window.open(url, '_blank', 'noopener,noreferrer')

      return {
        success: true,
        message: `Opened ${url}`
      }

    } catch (error: any) {
      console.error('❌ External action error:', error)
      return {
        success: false,
        error: `External action failed: ${error.message}`
      }
    }
  }

  /**
   * Open profile completion dialog
   */
  private async openProfileCompletionDialog(params?: Record<string, any>): Promise<void> {
    const router = useRouter()
    
    // Navigate to dashboard with profile completion focus
    await router.push({ 
      path: '/dashboard', 
      query: { action: 'complete-profile', ...params } 
    })
  }

  /**
   * Open message dialog
   */
  private async openMessageDialog(params?: Record<string, any>): Promise<void> {
    if (!params?.userId) {
      throw new Error('Message dialog requires userId parameter')
    }

    const MessageDialog = (await import('@/components/messaging/MessageDialog.vue')).default

    Dialog.create({
      component: MessageDialog,
      componentProps: {
        userId: params.userId,
        userName: params.userName || 'User'
      }
    })
  }

  /**
   * Open post creation dialog
   */
  private async openPostCreationDialog(params?: Record<string, any>): Promise<void> {
    const router = useRouter()
    
    // Navigate to community with post creation
    await router.push({ 
      path: '/virtual-community', 
      query: { 
        tab: 'feed', 
        action: 'create-post',
        type: params?.postType || 'general',
        ...params 
      } 
    })
  }

  /**
   * Generate context-aware action buttons based on user state
   */
  generateContextualActions(userContext: any, currentPage: string): AIActionButton[] {
    const actions: AIActionButton[] = []
    const authStore = useAuthStore()

    // Authentication-based actions
    if (!authStore.isAuthenticated) {
      actions.push({
        id: 'auth-signin',
        label: 'Sign In',
        icon: 'login',
        color: 'primary',
        action_type: 'dialog',
        action_data: { dialog: 'auth-signin' },
        tooltip: 'Sign in to access all features'
      })

      actions.push({
        id: 'auth-signup',
        label: 'Join Platform',
        icon: 'person_add',
        color: 'secondary',
        action_type: 'dialog',
        action_data: { dialog: 'auth-signup' },
        tooltip: 'Create your account'
      })
    } else {
      // Authenticated user actions
      const profile = authStore.profile

      // Profile completion actions
      if (!profile || (profile.profile_completion || 0) < 80) {
        actions.push({
          id: 'complete-profile',
          label: 'Complete Profile',
          icon: 'person',
          color: 'orange',
          action_type: 'dialog',
          action_data: { dialog: 'profile-completion' },
          tooltip: 'Complete your profile for better visibility',
          requires_auth: true
        })
      }

      // Page-specific actions
      if (currentPage === 'home') {
        actions.push({
          id: 'go-dashboard',
          label: 'Go to Dashboard',
          icon: 'dashboard',
          color: 'primary',
          action_type: 'navigation',
          action_data: { route: '/dashboard' },
          tooltip: 'Access your personalized dashboard',
          requires_auth: true
        })
      }

      if (currentPage !== 'virtual-community') {
        actions.push({
          id: 'explore-community',
          label: 'Explore Community',
          icon: 'people',
          color: 'secondary',
          action_type: 'navigation',
          action_data: { route: '/virtual-community', params: { tab: 'feed' } },
          tooltip: 'Discover the innovation community'
        })
      }
    }

    return actions
  }
}

// Create singleton instance
let _aiActionService: AIActionService | null = null

export const useAiActionService = (): AIActionService => {
  if (!_aiActionService) {
    _aiActionService = new AIActionService()
  }
  return _aiActionService
}

// Default export for compatibility
export default useAiActionService()
