/**
 * AI Chat Service - Clean Implementation
 * 
 * Handles all AI chat functionality with proper error handling
 * and response processing
 */

import { supabase } from '@/lib/supabase'
import { useAuthStore } from '@/stores/auth'

// Types
export interface ChatMessage {
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp?: Date
  id?: string
}

export interface UserContext {
  is_authenticated: boolean
  user_id?: string
  profile_type?: string
  current_page?: string
  profile_data?: any
}

export interface AIChatRequest {
  message: string
  conversation_history?: ChatMessage[]
  user_context: UserContext
  rag_enabled?: boolean
  max_context_items?: number
}

export interface AIActionButton {
  id: string
  label: string
  icon?: string
  color?: string
  action_type: 'navigation' | 'dialog' | 'trigger' | 'external'
  action_data: {
    route?: string
    dialog?: string
    trigger_key?: string
    url?: string
    params?: Record<string, any>
  }
  tooltip?: string
  requires_auth?: boolean
}

export interface AIChatResponse {
  success: boolean
  message?: string
  action_buttons?: AIActionButton[]
  rag_context_used?: any[]
  processing_time_ms?: number
  query_route?: string
  error?: string
}

class AIChatService {
  private baseUrl: string

  constructor() {
    this.baseUrl = `${import.meta.env.VITE_SUPABASE_URL}/functions/v1`
  }

  /**
   * Get user context for AI requests
   */
  private getUserContext(currentPage?: string): UserContext {
    const authStore = useAuthStore()
    const user = authStore.user
    const profile = authStore.profile

    return {
      is_authenticated: !!user,
      user_id: user?.id,
      profile_type: profile?.profile_type,
      current_page: currentPage || this.getCurrentPage(),
      profile_data: profile
    }
  }

  /**
   * Get current page from URL
   */
  private getCurrentPage(): string {
    const path = window.location.pathname
    if (path === '/') return 'home'
    if (path.includes('/dashboard')) return 'dashboard'
    if (path.includes('/virtual-community')) return 'virtual-community'
    if (path.includes('/about')) return 'about'
    return 'unknown'
  }

  /**
   * Send message to AI and get response
   */
  async sendMessage(
    message: string,
    conversationHistory: ChatMessage[] = [],
    currentPage?: string
  ): Promise<AIChatResponse> {
    try {
      console.log('🤖 Sending AI message:', { message: message.substring(0, 50) + '...', currentPage })

      const userContext = this.getUserContext(currentPage)
      
      const requestBody: AIChatRequest = {
        message: message.trim(),
        conversation_history: conversationHistory.slice(-6), // Keep last 6 messages
        user_context: userContext,
        rag_enabled: true,
        max_context_items: 8
      }

      console.log('📤 Request payload:', {
        messageLength: requestBody.message.length,
        historyLength: requestBody.conversation_history?.length || 0,
        userAuthenticated: userContext.is_authenticated,
        currentPage: userContext.current_page
      })

      console.log('🔧 Environment check:', {
        supabaseUrl: import.meta.env.VITE_SUPABASE_URL,
        hasAnonKey: !!import.meta.env.VITE_SUPABASE_ANON_KEY,
        anonKeyLength: import.meta.env.VITE_SUPABASE_ANON_KEY?.length || 0,
        fullUrl: `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/ai-chat`
      })

      // Call the AI chat Edge Function using direct fetch (like working examples)
      const httpResponse = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/ai-chat`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
          'apikey': import.meta.env.VITE_SUPABASE_ANON_KEY,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      })

      console.log('📥 Raw response status:', httpResponse.status, httpResponse.statusText)

      if (!httpResponse.ok) {
        const errorText = await httpResponse.text()
        console.error('❌ Edge Function error:', httpResponse.status, errorText)
        throw new Error(`AI service error: ${httpResponse.status} - ${errorText}`)
      }

      const data = await httpResponse.json()
      console.log('📥 Response data:', {
        success: data.success,
        hasMessage: !!data.message,
        messageLength: data.message?.length || 0
      })

      // Handle different response formats
      let response: AIChatResponse
      
      if (data.success === false) {
        console.error('❌ AI service returned error:', data.error)
        throw new Error(data.error || 'AI service returned an error')
      }

      if (data.message) {
        // New format: { success: true, message: "...", ... }
        response = data as AIChatResponse
        console.log('✅ AI response received (new format):', {
          messageLength: response.message?.length || 0,
          processingTime: response.processing_time_ms,
          queryRoute: response.query_route
        })
      } else if (data.response) {
        // Legacy format: { response: "...", ... }
        response = {
          success: true,
          message: data.response,
          processing_time_ms: data.processing_time_ms,
          query_route: data.query_route
        }
        console.log('✅ AI response received (legacy format):', {
          messageLength: response.message?.length || 0,
          processingTime: response.processing_time_ms
        })
      } else {
        console.error('❌ Invalid response format:', data)
        throw new Error('Invalid response format from AI service')
      }

      // Validate response content
      if (!response.message || response.message.trim().length === 0) {
        console.error('❌ Empty AI response received')
        throw new Error('AI service returned an empty response')
      }

      // Generate context-aware action buttons if not provided by AI
      if (!response.action_buttons || response.action_buttons.length === 0) {
        response.action_buttons = await this.generateContextualActionButtons(
          response.message,
          userContext,
          conversationHistory
        )
      }

      return response

    } catch (error: any) {
      console.error('💥 AI Chat Service Error:', {
        message: error.message,
        stack: error.stack,
        type: typeof error
      })

      // Return structured error response
      return {
        success: false,
        error: error.message || 'Unknown error occurred',
        processing_time_ms: 0
      }
    }
  }

  /**
   * Generate contextual action buttons based on AI response and user context
   */
  private async generateContextualActionButtons(
    aiMessage: string,
    userContext: UserContext,
    conversationHistory: ChatMessage[]
  ): Promise<AIActionButton[]> {
    try {
      const actions: AIActionButton[] = []
      const messageLower = aiMessage.toLowerCase()

      // Authentication-based actions
      if (!userContext.is_authenticated) {
        if (messageLower.includes('sign in') || messageLower.includes('log in') || messageLower.includes('account')) {
          actions.push({
            id: 'auth-signin',
            label: 'Sign In',
            icon: 'login',
            color: 'primary',
            action_type: 'dialog',
            action_data: { dialog: 'auth-signin' },
            tooltip: 'Sign in to access all features'
          })
        }

        if (messageLower.includes('join') || messageLower.includes('register') || messageLower.includes('sign up')) {
          actions.push({
            id: 'auth-signup',
            label: 'Join Platform',
            icon: 'person_add',
            color: 'secondary',
            action_type: 'dialog',
            action_data: { dialog: 'auth-signup' },
            tooltip: 'Create your account'
          })
        }
      } else {
        // Authenticated user actions

        // Profile-related actions
        if (messageLower.includes('profile') || messageLower.includes('complete')) {
          const profileCompletion = userContext.profile_data?.profile_completion || 0
          if (profileCompletion < 80) {
            actions.push({
              id: 'complete-profile',
              label: 'Complete Profile',
              icon: 'person',
              color: 'orange',
              action_type: 'navigation',
              action_data: { route: '/dashboard', params: { section: 'profile' } },
              tooltip: 'Complete your profile for better visibility',
              requires_auth: true
            })
          }
        }

        // Navigation actions based on content
        if (messageLower.includes('dashboard') && userContext.current_page !== 'dashboard') {
          actions.push({
            id: 'go-dashboard',
            label: 'Go to Dashboard',
            icon: 'dashboard',
            color: 'primary',
            action_type: 'navigation',
            action_data: { route: '/dashboard' },
            tooltip: 'Access your personalized dashboard',
            requires_auth: true
          })
        }

        if (messageLower.includes('community') || messageLower.includes('feed') || messageLower.includes('connect')) {
          actions.push({
            id: 'explore-community',
            label: 'Explore Community',
            icon: 'people',
            color: 'secondary',
            action_type: 'navigation',
            action_data: { route: '/virtual-community', params: { tab: 'feed' } },
            tooltip: 'Discover the innovation community'
          })
        }

        if (messageLower.includes('profile') || messageLower.includes('network') || messageLower.includes('connect')) {
          actions.push({
            id: 'browse-profiles',
            label: 'Browse Profiles',
            icon: 'people',
            color: 'blue',
            action_type: 'navigation',
            action_data: { route: '/virtual-community', params: { tab: 'profiles' } },
            tooltip: 'Browse and connect with other innovators'
          })
        }

        if (messageLower.includes('marketplace') || messageLower.includes('opportunity') || messageLower.includes('collaboration')) {
          actions.push({
            id: 'explore-marketplace',
            label: 'Explore Marketplace',
            icon: 'store',
            color: 'teal',
            action_type: 'navigation',
            action_data: { route: '/virtual-community', params: { tab: 'marketplace' } },
            tooltip: 'Find opportunities and collaborations'
          })
        }

        // Content creation actions
        if (messageLower.includes('post') || messageLower.includes('share') || messageLower.includes('create')) {
          actions.push({
            id: 'create-post',
            label: 'Create Post',
            icon: 'edit',
            color: 'green',
            action_type: 'dialog',
            action_data: { dialog: 'post-creation', params: { type: 'general' } },
            tooltip: 'Share your thoughts with the community',
            requires_auth: true
          })
        }
      }

      // Remove duplicates based on ID
      const uniqueActions = actions.filter((action, index, self) =>
        index === self.findIndex(a => a.id === action.id)
      )

      console.log('🎯 Generated contextual actions:', uniqueActions.map(a => a.label))
      return uniqueActions.slice(0, 4) // Limit to 4 actions to avoid UI clutter

    } catch (error) {
      console.error('❌ Error generating contextual action buttons:', error)
      return []
    }
  }

  /**
   * Test AI service connectivity
   */
  async testConnection(): Promise<boolean> {
    try {
      const response = await this.sendMessage('Hello, this is a connection test')
      return response.success && !!response.message
    } catch (error) {
      console.error('AI service connection test failed:', error)
      return false
    }
  }
}

// Singleton instance
let _aiChatService: AIChatService | null = null

export const getAiChatService = (): AIChatService => {
  if (!_aiChatService) {
    _aiChatService = new AIChatService()
  }
  return _aiChatService
}

// Global test function for debugging
declare global {
  interface Window {
    __testAIChat: () => Promise<void>
  }
}

// Add global test function for debugging
window.__testAIChat = async () => {
  console.log('🧪 Testing AI Chat from global function...')
  try {
    const service = getAiChatService()
    const response = await service.sendMessage('Hello, can you tell me about the ZbInnovation platform?')
    console.log('✅ AI Chat test successful:', response)
  } catch (error) {
    console.error('❌ AI Chat test failed:', error)
  }
}

export default getAiChatService
