import { supabase } from '@/lib/supabase'
import { useAuthStore } from '@/stores/auth'

// Type definitions
export interface ChatMessage {
  role: 'user' | 'assistant' | 'system'
  content: string
  metadata?: Record<string, any>
}

export interface UserContext {
  is_authenticated: boolean
  user_id?: string
  profile_type?: string
  profile_completion?: number
  current_page?: string
  profile_data?: any
}

export interface AIAction {
  type: 'navigation' | 'action' | 'external'
  label: string
  icon?: string
  url?: string
  action?: string
  color?: string
}

export interface AIChatRequest {
  message: string
  conversation_id?: string
  user_context: UserContext
  include_context?: boolean
  stream?: boolean
}

export interface AIChatResponse {
  response: string
  conversation_id: string
  actions?: AIAction[]
  suggestions?: string[]
  metadata?: Record<string, any>
  error?: string
}

export interface StreamingChatResponse {
  type: 'content' | 'complete' | 'error'
  content?: string
  conversation_id?: string
  actions?: AIAction[]
  suggestions?: string[]
  error?: string
}

class AIChatService {
  private baseUrl: string
  private authStore = useAuthStore()

  constructor() {
    this.baseUrl = `${import.meta.env.VITE_SUPABASE_URL}/functions/v1`
  }

  /**
   * Get user context for AI chat
   */
  private getUserContext(currentPage?: string): UserContext {
    const user = this.authStore.user
    const profile = this.authStore.profile

    return {
      is_authenticated: !!user,
      user_id: user?.id,
      profile_type: profile?.profile_type,
      profile_completion: profile?.completion_percentage || 0,
      current_page: currentPage || window.location.pathname.split('/')[1] || 'landing',
      profile_data: profile
    }
  }

  /**
   * Send a chat message and get AI response
   */
  async sendMessage(
    message: string, 
    conversationId?: string,
    currentPage?: string,
    includeContext: boolean = true
  ): Promise<AIChatResponse> {
    try {
      console.log('Sending AI chat message:', { message, conversationId, currentPage })

      const userContext = this.getUserContext(currentPage)
      
      const requestBody: AIChatRequest = {
        message,
        conversation_id: conversationId,
        user_context: userContext,
        include_context: includeContext,
        stream: false
      }

      const { data, error } = await supabase.functions.invoke('ai-enhanced-chat', {
        body: requestBody
      })

      if (error) {
        console.error('AI chat service error:', error)
        throw new Error(error.message || 'Failed to get AI response')
      }

      console.log('AI chat response received:', data)
      return data as AIChatResponse

    } catch (error) {
      console.error('Error in sendMessage:', error)
      
      // Return fallback response
      return {
        response: "I'm currently undergoing a system upgrade to serve you better. Please try again in a moment, or explore the platform using the buttons below.",
        conversation_id: conversationId || crypto.randomUUID(),
        actions: this.getFallbackActions(),
        suggestions: this.getFallbackSuggestions(),
        error: 'AI service temporarily unavailable'
      }
    }
  }

  /**
   * Send a streaming chat message
   */
  async sendStreamingMessage(
    message: string,
    conversationId?: string,
    currentPage?: string,
    onChunk?: (chunk: StreamingChatResponse) => void,
    includeContext: boolean = true
  ): Promise<string> {
    try {
      console.log('Sending streaming AI chat message:', { message, conversationId, currentPage })

      const userContext = this.getUserContext(currentPage)
      
      const requestBody: AIChatRequest = {
        message,
        conversation_id: conversationId,
        user_context: userContext,
        include_context: includeContext,
        stream: true
      }

      const response = await fetch(`${this.baseUrl}/ai-enhanced-chat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`,
          'Accept': 'text/event-stream'
        },
        body: JSON.stringify(requestBody)
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const reader = response.body?.getReader()
      const decoder = new TextDecoder()
      let fullResponse = ''

      if (!reader) {
        throw new Error('No response body')
      }

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value)
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6)
            if (data === '[DONE]') continue

            try {
              const parsed: StreamingChatResponse = JSON.parse(data)
              
              if (parsed.type === 'content' && parsed.content) {
                fullResponse += parsed.content
                onChunk?.(parsed)
              } else if (parsed.type === 'complete') {
                onChunk?.(parsed)
                break
              } else if (parsed.type === 'error') {
                onChunk?.(parsed)
                throw new Error(parsed.error || 'Streaming error')
              }
            } catch (e) {
              console.warn('Failed to parse streaming chunk:', e)
            }
          }
        }
      }

      return fullResponse

    } catch (error) {
      console.error('Error in sendStreamingMessage:', error)
      
      // Send fallback response through callback
      onChunk?.({
        type: 'error',
        error: 'AI service temporarily unavailable - system upgrade in progress'
      })
      
      throw error
    }
  }

  /**
   * Get conversation history
   */
  async getConversationHistory(conversationId: string): Promise<ChatMessage[]> {
    try {
      const { data, error } = await supabase.rpc('get_ai_conversation_context', {
        conversation_id_param: conversationId,
        message_limit: 50
      })

      if (error) {
        console.error('Error fetching conversation history:', error)
        return []
      }

      if (data && data.length > 0) {
        return data[0].messages || []
      }

      return []
    } catch (error) {
      console.error('Error in getConversationHistory:', error)
      return []
    }
  }

  /**
   * Get user's conversation list
   */
  async getUserConversations(limit: number = 20): Promise<any[]> {
    try {
      const user = this.authStore.user
      if (!user) return []

      const { data, error } = await supabase
        .from('ai_conversations')
        .select('*')
        .eq('user_id', user.id)
        .order('last_message_at', { ascending: false })
        .limit(limit)

      if (error) {
        console.error('Error fetching user conversations:', error)
        return []
      }

      return data || []
    } catch (error) {
      console.error('Error in getUserConversations:', error)
      return []
    }
  }

  /**
   * Delete a conversation
   */
  async deleteConversation(conversationId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('ai_conversations')
        .delete()
        .eq('id', conversationId)

      if (error) {
        console.error('Error deleting conversation:', error)
        return false
      }

      return true
    } catch (error) {
      console.error('Error in deleteConversation:', error)
      return false
    }
  }

  /**
   * Get fallback actions based on authentication status
   */
  private getFallbackActions(): AIAction[] {
    const userContext = this.getUserContext()
    
    if (userContext.is_authenticated) {
      return [
        { type: 'navigation', label: 'View Dashboard', icon: 'dashboard', url: '/dashboard', color: 'primary' },
        { type: 'navigation', label: 'Explore Community', icon: 'groups', url: '/virtual-community', color: 'secondary' }
      ]
    } else {
      return [
        { type: 'action', label: 'Sign Up', icon: 'person_add', action: 'signup', color: 'primary' },
        { type: 'navigation', label: 'Explore Community', icon: 'groups', url: '/virtual-community', color: 'secondary' }
      ]
    }
  }

  /**
   * Get fallback suggestions
   */
  private getFallbackSuggestions(): string[] {
    return [
      "What can I do on this platform?",
      "How do I get started?",
      "Tell me about the innovation community",
      "Help me navigate the platform"
    ]
  }
}

// Export singleton instance
export const aiChatService = new AIChatService()
export default aiChatService
