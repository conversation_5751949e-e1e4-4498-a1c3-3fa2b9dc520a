/**
 * AI Chat Trigger Service
 *
 * Handles triggering AI chat with contextual messages and suggestions
 */

interface TriggerContext {
  key: string
  context?: string
  profileType?: string
}

interface TriggerConfig {
  message: string
  suggestions: string[]
}

class AIChatTriggerService {
  private triggerConfigs: Record<string, TriggerConfig> = {
    // General triggers
    general_assistance: {
      message: "Hello! I'm here to help you navigate the ZbInnovation platform. What would you like to know?",
      suggestions: [
        "How do I complete my profile?",
        "Help me find connections",
        "Show me relevant opportunities",
        "How does the platform work?"
      ]
    },
    
    // Profile enhancement triggers
    complete_profile: {
      message: "I can help you complete your profile to maximize your visibility and connections. What section would you like to work on?",
      suggestions: [
        "What information should I include?",
        "How can I make my profile stand out?",
        "Show me profile examples",
        "Help me write a better bio"
      ]
    },
    
    optimize_profile: {
      message: "Let's optimize your profile for better visibility and engagement. I can help you improve various sections.",
      suggestions: [
        "Review my current profile",
        "Suggest improvements",
        "Help with keywords",
        "Optimize for my goals"
      ]
    },
    
    // Networking triggers
    find_connections: {
      message: "I can help you find and connect with relevant people in the innovation ecosystem. What type of connections are you looking for?",
      suggestions: [
        "Find mentors in my field",
        "Connect with potential collaborators",
        "Find investors for my project",
        "Network with industry experts"
      ]
    },
    
    networking: {
      message: "Let's expand your network! I can help you find the right people to connect with based on your interests and goals.",
      suggestions: [
        "Find people in my industry",
        "Connect with similar profiles",
        "Find collaboration opportunities",
        "Get networking tips"
      ]
    },
    
    // Content discovery triggers
    content_discovery: {
      message: "I can help you discover relevant content, discussions, and opportunities on the platform. What interests you?",
      suggestions: [
        "Find relevant posts",
        "Discover trending topics",
        "Find events in my area",
        "Show me marketplace items"
      ]
    },
    
    discover_content: {
      message: "Let me help you find content that matches your interests and goals. What type of content are you looking for?",
      suggestions: [
        "Latest industry news",
        "Innovation trends",
        "Learning resources",
        "Success stories"
      ]
    },
    
    // Opportunity triggers
    collaboration_opportunities: {
      message: "I can help you find collaboration opportunities that match your skills and interests. What type of collaboration are you seeking?",
      suggestions: [
        "Find project partners",
        "Join existing projects",
        "Start a new collaboration",
        "Find skill exchanges"
      ]
    },
    
    discover_opportunities: {
      message: "Let me help you discover opportunities in the innovation ecosystem. What type of opportunities interest you?",
      suggestions: [
        "Funding opportunities",
        "Partnership opportunities",
        "Learning opportunities",
        "Career opportunities"
      ]
    },
    
    // Getting started
    get_started: {
      message: "Welcome to ZbInnovation! I'm here to help you get started and make the most of the platform. What would you like to do first?",
      suggestions: [
        "Set up my profile",
        "Explore the platform",
        "Find my first connections",
        "Learn about features"
      ]
    }
  }

  /**
   * Trigger AI chat with contextual message and suggestions
   */
  async triggerChat(triggerKey: string, context?: string): Promise<void> {
    try {
      console.log('🎯 Triggering AI chat:', { triggerKey, context })

      // Get trigger configuration
      const config = this.getTriggerConfig(triggerKey, context)

      // Dispatch a custom event that the AI chat component can listen to
      const event = new CustomEvent('ai-chat-trigger', {
        detail: {
          triggerKey,
          context,
          message: config.message,
          suggestions: config.suggestions
        }
      })

      window.dispatchEvent(event)

      console.log('✅ AI chat triggered successfully')
    } catch (error) {
      console.error('❌ Error triggering AI chat:', error)
      throw error
    }
  }

  /**
   * Get trigger configuration for a specific key and context
   */
  private getTriggerConfig(triggerKey: string, context?: string): TriggerConfig {
    // Try to get specific configuration
    let config = this.triggerConfigs[triggerKey]
    
    if (!config) {
      // Fallback to general assistance
      config = this.triggerConfigs.general_assistance
    }
    
    // Customize message based on context if needed
    if (context) {
      config = {
        ...config,
        message: this.customizeMessageForContext(config.message, context)
      }
    }
    
    return config
  }

  /**
   * Customize message based on context
   */
  private customizeMessageForContext(message: string, context: string): string {
    // Add context-specific customization if needed
    if (context.includes('dashboard')) {
      return `From your dashboard: ${message}`
    }
    
    if (context.includes('community')) {
      return `In the community section: ${message}`
    }
    
    if (context.includes('profile')) {
      return `For your profile: ${message}`
    }
    
    return message
  }

  /**
   * Get available triggers for a specific profile type
   */
  getTriggersForProfile(profileType: string): string[] {
    // Return relevant trigger keys based on profile type
    const baseTriggers = ['general_assistance', 'find_connections', 'discover_content']
    
    switch (profileType) {
      case 'innovator':
        return [...baseTriggers, 'collaboration_opportunities', 'discover_opportunities']
      case 'investor':
        return [...baseTriggers, 'discover_projects', 'investment_opportunities']
      case 'mentor':
        return [...baseTriggers, 'mentorship_opportunities', 'find_mentees']
      default:
        return baseTriggers
    }
  }
}

// Create singleton instance
let _aiChatTriggerService: AIChatTriggerService | null = null

export const useAiChatTriggerService = (): AIChatTriggerService => {
  if (!_aiChatTriggerService) {
    _aiChatTriggerService = new AIChatTriggerService()
  }
  return _aiChatTriggerService
}

// Default export for compatibility
export default useAiChatTriggerService()
