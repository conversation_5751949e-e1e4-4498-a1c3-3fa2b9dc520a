// Comprehensive test for production-ready AI system
const SUPABASE_URL = 'https://dpicnvisvxpmgjtbeicf.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRwaWNudmlzdnhwbWdqdGJlaWNmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM3OTE0MDgsImV4cCI6MjA1OTM2NzQwOH0.6x_KSp-mDrLNqZiSW3ZB4VFDPM5tQas0mc68Md-Uvrc';

// Helper function to make authenticated requests to Edge Functions
async function callEdgeFunction(functionName, body = {}, method = 'POST') {
  const headers = {
    'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
    'apikey': SUPABASE_ANON_KEY,
    'Content-Type': 'application/json',
  };

  const config = {
    method,
    headers
  };

  if (method !== 'GET' && body) {
    config.body = JSON.stringify(body);
  }

  const response = await fetch(`${SUPABASE_URL}/functions/v1/${functionName}`, config);

  return {
    ok: response.ok,
    status: response.status,
    statusText: response.statusText,
    data: response.ok ? await response.json() : await response.text()
  };
}

// Test queries for different routes
const TEST_QUERIES = {
  rag: [
    "Tell me about fintech innovators in Zimbabwe",
    "Find me mentors with AI expertise",
    "What innovation opportunities are available?",
    "Help me connect with investors interested in agriculture"
  ],
  text2sql: [
    "How many innovators joined last month?",
    "What are the top 5 most active profile types?",
    "Show me statistics about platform growth",
    "Count the total number of posts by category"
  ],
  hybrid: [
    "Show me top-funded startups and their profiles",
    "Find successful innovators with their metrics",
    "Recommend investors based on funding data",
    "Analyze the most connected mentors"
  ]
};

async function testProductionEmbeddings() {
  console.log('🔢 Testing Production Embedding Models...');

  const testCases = [
    {
      text: "I am an AI researcher working on machine learning applications for agriculture in Zimbabwe",
      model: "bge-m3",
      chunk_strategy: "profile"
    },
    {
      text: "Looking for investment opportunities in fintech startups with strong growth potential",
      model: "bge-m3",
      chunk_strategy: "content"
    }
  ];

  for (const testCase of testCases) {
    try {
      console.log(`\n📊 Testing: ${testCase.model} with ${testCase.chunk_strategy} chunking`);

      const result = await callEdgeFunction('embed', testCase);

      if (result.ok) {
        const data = result.data;
        console.log(`✅ Success: ${data.dimensions} dimensions, ${data.processing_time_ms}ms`);
        console.log(`   Model: ${data.model}, Chunks: ${data.chunks?.length || 1}`);
      } else {
        console.log(`❌ Failed: ${result.status} - ${result.data}`);
      }
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
    }
  }
}

async function testQueryRouting() {
  console.log('\n🧭 Testing Query Routing System...');

  for (const [routeType, queries] of Object.entries(TEST_QUERIES)) {
    console.log(`\n📋 Testing ${routeType.toUpperCase()} queries:`);

    for (const query of queries.slice(0, 2)) { // Test 2 queries per type
      try {
        const result = await callEdgeFunction('query-router', { query });

        if (result.ok) {
          const data = result.data;
          const isCorrect = data.route === routeType;
          const status = isCorrect ? '✅' : '⚠️';
          console.log(`${status} "${query.substring(0, 50)}..."`);
          console.log(`   Routed to: ${data.route} (${(data.confidence * 100).toFixed(0)}% confidence)`);
          console.log(`   Reasoning: ${data.reasoning}`);
        } else {
          console.log(`❌ Routing failed: ${result.status} - ${result.data}`);
        }
      } catch (error) {
        console.log(`❌ Error: ${error.message}`);
      }
    }
  }
}

async function testEnhancedAIChat() {
  console.log('\n🤖 Testing Enhanced AI Chat System...');
  
  const testCases = [
    {
      message: "Tell me about innovation opportunities in Zimbabwe",
      expectedRoute: "rag",
      description: "Content-focused query"
    },
    {
      message: "How many investors are on the platform?",
      expectedRoute: "text2sql", 
      description: "Analytics query"
    },
    {
      message: "Show me top-performing startups with their funding data",
      expectedRoute: "hybrid",
      description: "Complex hybrid query"
    }
  ];

  for (const testCase of testCases) {
    try {
      console.log(`\n💬 Testing: ${testCase.description}`);
      console.log(`   Query: "${testCase.message}"`);
      
      const response = await fetch(`${SUPABASE_URL}/functions/v1/ai-chat`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: testCase.message,
          user_context: { user_id: null },
          rag_enabled: true,
          max_context_items: 5
        })
      });

      if (response.ok) {
        const data = await response.json();
        const routeMatch = data.query_route === testCase.expectedRoute;
        const routeStatus = routeMatch ? '✅' : '⚠️';
        
        console.log(`${routeStatus} Route: ${data.query_route} (expected: ${testCase.expectedRoute})`);
        console.log(`   Confidence: ${(data.route_confidence * 100).toFixed(0)}%`);
        console.log(`   Context items: ${data.rag_context_used?.length || 0}`);
        console.log(`   Processing time: ${data.processing_time_ms}ms`);
        console.log(`   Response preview: "${data.message?.substring(0, 100)}..."`);
        
        if (data.rag_context_used?.length > 0) {
          console.log(`   Context sources: ${data.rag_context_used.map(c => c.source).join(', ')}`);
        }
      } else {
        const errorText = await response.text();
        console.log(`❌ AI Chat failed: ${response.status} - ${errorText}`);
      }
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
    }
  }
}

async function testRAGPopulation() {
  console.log('\n📊 Testing Enhanced RAG Population...');
  
  try {
    // Test with dry run first
    const dryRunResponse = await fetch(`${SUPABASE_URL}/functions/v1/populate-rag-embeddings`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        content_types: ['profile'],
        batch_size: 2,
        dry_run: true
      })
    });

    if (dryRunResponse.ok) {
      const dryRunData = await dryRunResponse.json();
      console.log('✅ RAG Population Dry Run:');
      console.log(`   Content types processed: ${dryRunData.content_processed.length}`);
      
      dryRunData.content_processed.forEach(content => {
        console.log(`   ${content.content_type}: ${content.records_found} records found`);
      });
      
      console.log(`   Processing time: ${dryRunData.processing_time_ms}ms`);
    } else {
      console.log(`❌ RAG Population dry run failed: ${dryRunResponse.status}`);
    }
  } catch (error) {
    console.log(`❌ RAG Population test failed: ${error.message}`);
  }
}

async function testSystemHealth() {
  console.log('\n🏥 Testing System Health...');
  
  try {
    // Test RAG system status
    const ragStatusResponse = await fetch(`${SUPABASE_URL}/functions/v1/populate-rag-embeddings`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
      }
    });

    if (ragStatusResponse.ok) {
      const ragStatus = await ragStatusResponse.json();
      console.log('✅ RAG System Status:');
      console.log(`   System health: ${ragStatus.success ? 'Healthy' : 'Issues detected'}`);
      
      if (ragStatus.rag_system_status) {
        ragStatus.rag_system_status.forEach(metric => {
          console.log(`   ${metric.metric_name}: ${metric.metric_value} (${metric.status})`);
        });
      }
    } else {
      console.log(`❌ RAG system health check failed: ${ragStatusResponse.status}`);
    }
  } catch (error) {
    console.log(`❌ System health check failed: ${error.message}`);
  }
}

// Run comprehensive production tests
async function runProductionTests() {
  console.log('🚀 Starting Production System Tests...\n');
  console.log('=' .repeat(60));
  
  await testProductionEmbeddings();
  console.log('\n' + '=' .repeat(60));
  
  await testQueryRouting();
  console.log('\n' + '=' .repeat(60));
  
  await testEnhancedAIChat();
  console.log('\n' + '=' .repeat(60));
  
  await testRAGPopulation();
  console.log('\n' + '=' .repeat(60));
  
  await testSystemHealth();
  console.log('\n' + '=' .repeat(60));
  
  console.log('\n✅ Production system tests completed!');
  console.log('\n📊 Summary:');
  console.log('- Production embedding models (HuggingFace bge-m3)');
  console.log('- Intelligent query routing (RAG/Text2SQL/Hybrid)');
  console.log('- Enhanced contextual retrieval');
  console.log('- Semantic chunking with overlap');
  console.log('- Route-aware AI responses');
  console.log('- Comprehensive monitoring');
}

runProductionTests();
