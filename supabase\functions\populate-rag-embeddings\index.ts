import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'jsr:@supabase/supabase-js@2';

// Types for RAG content population
interface PopulateRAGRequest {
  content_types?: string[]; // ['profiles', 'posts', 'events']
  batch_size?: number;
  force_regenerate?: boolean;
  dry_run?: boolean;
}

interface PopulateRAGResponse {
  success: boolean;
  content_processed: {
    content_type: string;
    records_found: number;
    embeddings_created: number;
    errors: string[];
  }[];
  total_embeddings_created: number;
  processing_time_ms: number;
  errors: string[];
}

// Configuration for RAG content types
const RAG_CONTENT_CONFIGS = [
  {
    content_type: 'profile',
    source_tables: [
      {
        table: 'innovator_profiles',
        profile_type: 'innovator',
        content_fields: ['bio', 'innovation_area', 'innovation_description', 'short_term_goals', 'current_challenges'],
        title_field: 'profile_name'
      },
      {
        table: 'investor_profiles', 
        profile_type: 'investor',
        content_fields: ['bio', 'investment_focus', 'investment_criteria', 'investment_philosophy'],
        title_field: 'profile_name'
      },
      {
        table: 'mentor_profiles',
        profile_type: 'mentor', 
        content_fields: ['bio', 'areas_of_expertise', 'mentoring_approach', 'collaboration_interests'],
        title_field: 'profile_name'
      },
      {
        table: 'professional_profiles',
        profile_type: 'professional',
        content_fields: ['bio', 'skills', 'achievements', 'career_goals'],
        title_field: 'profile_name'
      }
    ]
  },
  {
    content_type: 'post',
    source_tables: [
      {
        table: 'posts',
        content_fields: ['title', 'content', 'tags'],
        title_field: 'title',
        author_field: 'author_id'
      }
    ]
  }
];

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

/**
 * Generate embedding using Supabase AI
 */
async function generateEmbedding(text: string): Promise<number[]> {
  try {
    const { data, error } = await supabase.functions.invoke('embed', {
      body: {
        input: text,
        model: 'gte-small'
      }
    });

    if (error) {
      console.error('Embedding generation error:', error);
      throw new Error(`Failed to generate embedding: ${error.message}`);
    }

    return data.embedding;
  } catch (error) {
    console.error('Error calling embedding function:', error);
    throw error;
  }
}

/**
 * Preprocess and combine fields for RAG content
 */
function createRAGContent(record: any, config: any): string {
  const contentParts: string[] = [];
  
  // Add title if available
  if (config.title_field && record[config.title_field]) {
    contentParts.push(`Title: ${record[config.title_field]}`);
  }
  
  // Add profile type for profiles
  if (config.profile_type) {
    contentParts.push(`Profile Type: ${config.profile_type}`);
  }
  
  // Process content fields
  config.content_fields.forEach((field: string) => {
    const value = record[field];
    if (value) {
      let processedValue = '';
      
      if (typeof value === 'string') {
        processedValue = value;
      } else if (Array.isArray(value)) {
        processedValue = value.join(', ');
      } else if (typeof value === 'object') {
        processedValue = JSON.stringify(value);
      } else {
        processedValue = String(value);
      }
      
      if (processedValue.trim()) {
        contentParts.push(`${field}: ${processedValue.trim()}`);
      }
    }
  });
  
  return contentParts.join('\n').trim();
}

/**
 * Create metadata for RAG embedding
 */
function createRAGMetadata(record: any, config: any, contentType: string): any {
  const metadata: any = {
    type: contentType,
    source_table: config.table,
    created_at: new Date().toISOString()
  };
  
  // Add user_id if available
  if (record.user_id) {
    metadata.user_id = record.user_id;
  }
  
  // Add author_id for posts
  if (config.author_field && record[config.author_field]) {
    metadata.author_id = record[config.author_field];
  }
  
  // Add profile type for profiles
  if (config.profile_type) {
    metadata.profile_type = config.profile_type;
  }
  
  // Add title for reference
  if (config.title_field && record[config.title_field]) {
    metadata.title = record[config.title_field];
  }
  
  return metadata;
}

/**
 * Process records from a source table
 */
async function processSourceTable(
  config: any,
  contentType: string,
  batchSize: number,
  forceRegenerate: boolean
): Promise<{ processed: number; errors: string[] }> {
  const errors: string[] = [];
  let processed = 0;
  
  try {
    console.log(`Processing ${config.table} for RAG embeddings...`);
    
    // Get records from source table
    const { data: records, error } = await supabase
      .from(config.table)
      .select('*')
      .limit(batchSize);
    
    if (error) {
      throw new Error(`Failed to fetch from ${config.table}: ${error.message}`);
    }
    
    if (!records || records.length === 0) {
      console.log(`No records found in ${config.table}`);
      return { processed: 0, errors: [] };
    }
    
    console.log(`Found ${records.length} records in ${config.table}`);
    
    // Process each record
    for (const record of records) {
      try {
        // Check if embedding already exists (unless force regenerate)
        if (!forceRegenerate) {
          const { data: existing } = await supabase
            .from('embeddings')
            .select('id')
            .eq('source_table', config.table)
            .eq('source_id', record.id)
            .single();
          
          if (existing) {
            console.log(`Embedding already exists for ${config.table}:${record.id}`);
            continue;
          }
        }
        
        // Create RAG content
        const ragContent = createRAGContent(record, config);
        
        if (ragContent.length < 10) {
          console.log(`Insufficient content for ${config.table}:${record.id}`);
          continue;
        }
        
        // Generate embedding
        const embedding = await generateEmbedding(ragContent);
        
        // Create metadata
        const metadata = createRAGMetadata(record, config, contentType);
        
        // Insert into embeddings table
        const { error: insertError } = await supabase
          .from('embeddings')
          .upsert({
            content: ragContent,
            metadata,
            embedding: `[${embedding.join(',')}]`,
            source_table: config.table,
            source_id: record.id
          }, {
            onConflict: 'source_table,source_id'
          });
        
        if (insertError) {
          throw new Error(`Failed to insert embedding: ${insertError.message}`);
        }
        
        processed++;
        console.log(`Created RAG embedding for ${config.table}:${record.id}`);
        
        // Small delay to avoid overwhelming the system
        await new Promise(resolve => setTimeout(resolve, 100));
        
      } catch (error) {
        const errorMsg = `Failed to process ${config.table}:${record.id}: ${error.message}`;
        errors.push(errorMsg);
        console.error(errorMsg);
      }
    }
    
    return { processed, errors };
    
  } catch (error) {
    const errorMsg = `Failed to process table ${config.table}: ${error.message}`;
    errors.push(errorMsg);
    return { processed: 0, errors };
  }
}

/**
 * Main Edge Function handler
 */
Deno.serve(async (req: Request) => {
  const startTime = Date.now();
  
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
        'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
      },
    });
  }
  
  // Handle GET request for status
  if (req.method === 'GET') {
    try {
      const stats = await supabase.rpc('analyze_rag_system');
      
      return new Response(
        JSON.stringify({
          success: true,
          rag_system_status: stats.data,
          timestamp: new Date().toISOString()
        }),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
          },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message
        }),
        {
          status: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
          },
        }
      );
    }
  }
  
  if (req.method !== 'POST') {
    return new Response(
      JSON.stringify({ error: 'Method not allowed' }),
      { 
        status: 405,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        }
      }
    );
  }
  
  try {
    const body = await req.json() as PopulateRAGRequest;
    const { 
      content_types = ['profile', 'post'], 
      batch_size = 10,
      force_regenerate = false,
      dry_run = false
    } = body;
    
    console.log(`Starting RAG population for: ${content_types.join(', ')}`);
    
    const contentProcessed = [];
    let totalEmbeddingsCreated = 0;
    const allErrors: string[] = [];
    
    // Process each content type
    for (const contentType of content_types) {
      const config = RAG_CONTENT_CONFIGS.find(c => c.content_type === contentType);
      
      if (!config) {
        allErrors.push(`Unknown content type: ${contentType}`);
        continue;
      }
      
      let typeEmbeddingsCreated = 0;
      const typeErrors: string[] = [];
      let typeRecordsFound = 0;
      
      // Process each source table for this content type
      for (const sourceConfig of config.source_tables) {
        if (dry_run) {
          // Just count records for dry run
          const { count } = await supabase
            .from(sourceConfig.table)
            .select('*', { count: 'exact', head: true });
          typeRecordsFound += count || 0;
        } else {
          const result = await processSourceTable(
            sourceConfig,
            contentType,
            batch_size,
            force_regenerate
          );
          
          typeEmbeddingsCreated += result.processed;
          typeErrors.push(...result.errors);
        }
      }
      
      contentProcessed.push({
        content_type: contentType,
        records_found: typeRecordsFound,
        embeddings_created: typeEmbeddingsCreated,
        errors: typeErrors
      });
      
      totalEmbeddingsCreated += typeEmbeddingsCreated;
      allErrors.push(...typeErrors);
    }
    
    const processingTime = Date.now() - startTime;
    
    const response: PopulateRAGResponse = {
      success: allErrors.length === 0,
      content_processed: contentProcessed,
      total_embeddings_created: totalEmbeddingsCreated,
      processing_time_ms: processingTime,
      errors: allErrors
    };
    
    console.log(`RAG population completed:`, response);
    
    return new Response(
      JSON.stringify(response),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      }
    );
    
  } catch (error) {
    console.error('RAG population error:', error);
    
    const processingTime = Date.now() - startTime;
    
    return new Response(
      JSON.stringify({
        success: false,
        content_processed: [],
        total_embeddings_created: 0,
        processing_time_ms: processingTime,
        errors: [error.message]
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      }
    );
  }
});
