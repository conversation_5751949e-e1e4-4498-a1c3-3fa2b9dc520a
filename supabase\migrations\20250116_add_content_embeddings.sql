-- Migration: Add Content Embeddings for AI-Enhanced Content Discovery
-- File: supabase/migrations/20250116_add_content_embeddings.sql
-- Description: Adds vector embedding columns to content tables for semantic search and recommendations

-- Ensure vector extension is available
CREATE EXTENSION IF NOT EXISTS vector;

-- =============================================================================
-- POSTS TABLE EMBEDDINGS
-- =============================================================================

ALTER TABLE posts 
ADD COLUMN IF NOT EXISTS content_embedding vector(384),
ADD COLUMN IF NOT EXISTS title_embedding vector(384),
ADD COLUMN IF NOT EXISTS tags_embedding vector(384),
ADD COLUMN IF NOT EXISTS embedding_metadata jsonb DEFAULT '{}',
ADD COLUMN IF NOT EXISTS embeddings_generated_at timestamp with time zone,
ADD COLUMN IF NOT EXISTS embedding_model_version varchar(50) DEFAULT 'gte-small-v1';

-- Create indexes for efficient vector similarity search
CREATE INDEX IF NOT EXISTS posts_content_embedding_idx 
ON posts USING ivfflat (content_embedding vector_cosine_ops) WITH (lists = 100);

CREATE INDEX IF NOT EXISTS posts_title_embedding_idx 
ON posts USING ivfflat (title_embedding vector_cosine_ops) WITH (lists = 100);

CREATE INDEX IF NOT EXISTS posts_tags_embedding_idx 
ON posts USING ivfflat (tags_embedding vector_cosine_ops) WITH (lists = 100);

-- =============================================================================
-- MARKETPLACE LISTINGS EMBEDDINGS (if exists)
-- =============================================================================

-- Check if marketplace_listings table exists and add embeddings
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'marketplace_listings') THEN
        -- Add embedding columns
        ALTER TABLE marketplace_listings 
        ADD COLUMN IF NOT EXISTS listing_embedding vector(384),
        ADD COLUMN IF NOT EXISTS description_embedding vector(384),
        ADD COLUMN IF NOT EXISTS embedding_metadata jsonb DEFAULT '{}',
        ADD COLUMN IF NOT EXISTS embeddings_generated_at timestamp with time zone,
        ADD COLUMN IF NOT EXISTS embedding_model_version varchar(50) DEFAULT 'gte-small-v1';

        -- Create indexes
        CREATE INDEX IF NOT EXISTS marketplace_listings_listing_embedding_idx 
        ON marketplace_listings USING ivfflat (listing_embedding vector_cosine_ops) WITH (lists = 100);

        CREATE INDEX IF NOT EXISTS marketplace_listings_description_embedding_idx 
        ON marketplace_listings USING ivfflat (description_embedding vector_cosine_ops) WITH (lists = 100);
    END IF;
END $$;

-- =============================================================================
-- GROUPS EMBEDDINGS (if exists)
-- =============================================================================

-- Check if groups table exists and add embeddings
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'groups') THEN
        -- Add embedding columns
        ALTER TABLE groups 
        ADD COLUMN IF NOT EXISTS group_embedding vector(384),
        ADD COLUMN IF NOT EXISTS description_embedding vector(384),
        ADD COLUMN IF NOT EXISTS embedding_metadata jsonb DEFAULT '{}',
        ADD COLUMN IF NOT EXISTS embeddings_generated_at timestamp with time zone,
        ADD COLUMN IF NOT EXISTS embedding_model_version varchar(50) DEFAULT 'gte-small-v1';

        -- Create indexes
        CREATE INDEX IF NOT EXISTS groups_group_embedding_idx 
        ON groups USING ivfflat (group_embedding vector_cosine_ops) WITH (lists = 100);

        CREATE INDEX IF NOT EXISTS groups_description_embedding_idx 
        ON groups USING ivfflat (description_embedding vector_cosine_ops) WITH (lists = 100);
    END IF;
END $$;

-- =============================================================================
-- EVENTS EMBEDDINGS (if exists)
-- =============================================================================

-- Check if events table exists and add embeddings
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'events') THEN
        -- Add embedding columns
        ALTER TABLE events 
        ADD COLUMN IF NOT EXISTS event_embedding vector(384),
        ADD COLUMN IF NOT EXISTS description_embedding vector(384),
        ADD COLUMN IF NOT EXISTS embedding_metadata jsonb DEFAULT '{}',
        ADD COLUMN IF NOT EXISTS embeddings_generated_at timestamp with time zone,
        ADD COLUMN IF NOT EXISTS embedding_model_version varchar(50) DEFAULT 'gte-small-v1';

        -- Create indexes
        CREATE INDEX IF NOT EXISTS events_event_embedding_idx 
        ON events USING ivfflat (event_embedding vector_cosine_ops) WITH (lists = 100);

        CREATE INDEX IF NOT EXISTS events_description_embedding_idx 
        ON events USING ivfflat (description_embedding vector_cosine_ops) WITH (lists = 100);
    END IF;
END $$;

-- =============================================================================
-- COMMENTS AND DOCUMENTATION
-- =============================================================================

-- Add comments for documentation
COMMENT ON COLUMN posts.content_embedding IS 'Vector embedding of post content for semantic search and recommendations';
COMMENT ON COLUMN posts.title_embedding IS 'Vector embedding of post title for quick semantic matching';
COMMENT ON COLUMN posts.tags_embedding IS 'Vector embedding of post tags combined as text for topic discovery';
COMMENT ON COLUMN posts.embedding_metadata IS 'Metadata about embedding generation (source fields, processing info)';
COMMENT ON COLUMN posts.embeddings_generated_at IS 'Timestamp when embeddings were last generated';
COMMENT ON COLUMN posts.embedding_model_version IS 'Version of the embedding model used';

-- =============================================================================
-- EMBEDDING TRACKING TABLE
-- =============================================================================

-- Create a table to track embedding generation status across all tables
CREATE TABLE IF NOT EXISTS embedding_generation_log (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    table_name varchar(100) NOT NULL,
    record_id uuid NOT NULL,
    embedding_type varchar(50) NOT NULL, -- 'profile', 'content', 'title', etc.
    status varchar(20) NOT NULL DEFAULT 'pending', -- 'pending', 'processing', 'completed', 'failed'
    error_message text,
    processing_started_at timestamp with time zone,
    processing_completed_at timestamp with time zone,
    model_version varchar(50) DEFAULT 'gte-small-v1',
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- Create indexes for the tracking table
CREATE INDEX IF NOT EXISTS embedding_generation_log_table_record_idx 
ON embedding_generation_log(table_name, record_id);

CREATE INDEX IF NOT EXISTS embedding_generation_log_status_idx 
ON embedding_generation_log(status);

CREATE INDEX IF NOT EXISTS embedding_generation_log_created_at_idx 
ON embedding_generation_log(created_at DESC);

-- Add RLS policy for the tracking table
ALTER TABLE embedding_generation_log ENABLE ROW LEVEL SECURITY;

-- Allow service role to manage embedding generation
CREATE POLICY "Service role can manage embedding generation log" ON embedding_generation_log
    FOR ALL USING (auth.role() = 'service_role');

-- =============================================================================
-- UTILITY FUNCTIONS
-- =============================================================================

-- Function to check if embeddings need regeneration
CREATE OR REPLACE FUNCTION needs_embedding_regeneration(
    table_name text,
    record_id uuid,
    last_updated timestamp with time zone,
    embeddings_generated_at timestamp with time zone
) RETURNS boolean AS $$
BEGIN
    -- Return true if embeddings were never generated or if content was updated after embeddings
    RETURN embeddings_generated_at IS NULL OR last_updated > embeddings_generated_at;
END;
$$ LANGUAGE plpgsql;

-- Function to mark embedding generation as needed
CREATE OR REPLACE FUNCTION mark_embedding_regeneration_needed()
RETURNS trigger AS $$
BEGIN
    -- Insert or update tracking record when content changes
    INSERT INTO embedding_generation_log (table_name, record_id, embedding_type, status)
    VALUES (TG_TABLE_NAME, NEW.id, 'all', 'pending')
    ON CONFLICT (table_name, record_id, embedding_type) 
    DO UPDATE SET 
        status = 'pending',
        updated_at = now();
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- =============================================================================
-- TRIGGERS FOR AUTOMATIC EMBEDDING REGENERATION
-- =============================================================================

-- Create triggers to automatically mark embeddings for regeneration when content changes

-- Posts table trigger
DROP TRIGGER IF EXISTS posts_embedding_regeneration_trigger ON posts;
CREATE TRIGGER posts_embedding_regeneration_trigger
    AFTER UPDATE OF title, content, tags ON posts
    FOR EACH ROW
    WHEN (OLD.title IS DISTINCT FROM NEW.title OR 
          OLD.content IS DISTINCT FROM NEW.content OR 
          OLD.tags IS DISTINCT FROM NEW.tags)
    EXECUTE FUNCTION mark_embedding_regeneration_needed();

-- Profile tables triggers (will be added for each profile table)
-- Note: These will be created in a separate migration after profile tables are updated

COMMENT ON TABLE embedding_generation_log IS 'Tracks embedding generation status across all tables with embeddings';
COMMENT ON FUNCTION needs_embedding_regeneration IS 'Utility function to check if embeddings need regeneration based on content updates';
COMMENT ON FUNCTION mark_embedding_regeneration_needed IS 'Trigger function to mark embeddings for regeneration when content changes';
